{"cells": [{"cell_type": "markdown", "id": "6aa6001a", "metadata": {}, "source": ["# Classificatiom Model 3: Random Forest\n", "\n", "***\n", "Let's develop our random forest model for the laundromat use case. \n"]}, {"cell_type": "code", "execution_count": null, "id": "5f3e7d88", "metadata": {}, "outputs": [], "source": ["from google.colab import drive\n", "\n", "drive.mount(??, force_remount=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f26c592f", "metadata": {}, "outputs": [], "source": ["ROOT_DIR = ??"]}, {"cell_type": "code", "execution_count": 11, "id": "258c8425", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     index_col       avC         avP       sdC         sdP          avR  maxC  \\\n", "432          3  3.595600  792.930000  3.332669  786.913383   399.232039  7.23   \n", "22           9  1.616815  329.370370  2.485054  583.668067   637.157143  1.76   \n", "620         14  1.305465  254.702326  2.232888  529.072646   604.123431  0.66   \n", "963          0  0.284667   34.533333  0.227744   33.000244  1429.358887  0.77   \n", "188          9  1.484593  298.003704  2.357156  558.706609   623.840883  1.84   \n", "..         ...       ...         ...       ...         ...          ...   ...   \n", "360         21  0.906095  167.071429  1.721403  405.103331   871.333092  0.09   \n", "466         15  1.292000  256.215556  2.277856  534.821437   658.162380  1.30   \n", "299          0  2.612000  574.333333  3.216506  759.213038   753.064179  7.25   \n", "493         20  1.080317  202.135000  1.919027  450.266332   678.029294  2.49   \n", "527          9  1.641481  339.711111  2.584596  619.005346   585.429776  0.59   \n", "\n", "     maxP     stdCR       stdCP         AvRR  \n", "432  1642  3.382349  805.313577   362.408577  \n", "22    271  0.587681   92.810374   449.344412  \n", "620    69  0.141624   18.216104   682.374211  \n", "963   102  0.227744   33.000244  1429.358887  \n", "188   286  0.601370   96.551553   475.380790  \n", "..    ...       ...         ...          ...  \n", "360     1  0.000000    0.000000  2628.307407  \n", "466   195  0.325882   50.023153   638.702682  \n", "299  1666  3.216506  759.213038   753.064179  \n", "493   395  0.630406  102.162728   226.295845  \n", "527    75  0.109605   15.296908   559.013457  \n", "\n", "[773 rows x 11 columns]\n", "432     CottonWash\n", "22      DailyRinse\n", "620    CottonRinse\n", "963    BeddingWash\n", "188     DailyRinse\n", "          ...     \n", "360     CottonSpin\n", "466    CottonRinse\n", "299     CottonWash\n", "493     CottonSpin\n", "527     CottonWash\n", "Name: mode, Length: 773, dtype: object\n"]}], "source": ["# Import necessary Python libraries here ...\n", "# ...\n", "# ...\n", "# ...\n", "\n", "# Loading the dataset from the /data folder here \n", "data_path  = ?\n", "\n", "# Read your csv file here ...\n", "currentdf  = ???\n", "\n", "# Allocate your training data and label \n", "x = ?\n", "y = ?\n", "\n", "# Splitting dataset into 75% for training and 25% for testing here ...\n", "# ...\n", "# ...\n", "\n", "# Display the features and label from the training set \n", "print(?)\n", "print(?)\n", "\n", "# Insert code to standardize your dataset here ...\n", "# ...\n", "# ...\n"]}, {"cell_type": "markdown", "id": "20a45993", "metadata": {}, "source": ["Scikit-learn library usually give a good default parameters for you to work on. You may refer to https://scikit-learn.org/stable/modules/generated/sklearn.ensemble.RandomForestClassifier.html for the different types of parameters available for random forest classifier. \n"]}, {"cell_type": "code", "execution_count": 12, "id": "6cc13136", "metadata": {}, "outputs": [], "source": ["from sklearn.?? import ??Classifier\n", "from sklearn.metrics import accuracy_score \n", "\n", "# define the tree depths to evaluate\n", "values = [i for i in range(0, 10)]\n", "mdepth = [1, 3, 4, 5, 6, 7, 8, 9, 10, 11]\n", "train_scores, test_scores = list(), list()\n", "\n", "for i in values:\n", "    # Create an instance of the predictor\n", "    rf = RandomForestClassifier(n_estimators=??,\n", "                            max_depth=??,\n", "                            random_state=??)\n", "     \n", "    # Use the training data to train the predictor\n", "    rf.fit(X_train_sc, y_train)\n", "    \n", "    # Predicting on the train dataset\n", "    y_pred_train = rf.predict(X_train_sc)   \n", "    train_acc = accuracy_score(y_train, y_pred_train)\n", "    train_scores.append(train_acc)\n", "    \n", "    # Predicting on the test dataset\n", "    y_pred_test = rf.predict(X_test_sc)    \n", "    test_acc = accuracy_score(y_test, y_pred_test)\n", "    test_scores.append(test_acc)\n", "    "]}, {"cell_type": "code", "execution_count": 13, "id": "cc75478a", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# plot of train and test scores vs tree depth\n", "plt.plot(values, train_scores, '-o', label='Train')\n", "plt.plot(values, test_scores, '-o', label='Test')\n", "plt.legend()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "f2dc0885", "metadata": {}, "source": ["We get approximately 0.85 or 85% for the test set, which is not too far from what we got from the other models."]}, {"cell_type": "markdown", "id": "93f7c568", "metadata": {}, "source": ["## Model Evaluation\n", "\n", "### Confusion Matrix\n", "\n", "Let's compute the confusion matrix for this Decision Tree model for the laudromat use case.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "5b625252", "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Model Score 0.1744186046511628\n"]}], "source": ["from sklearn.?? import ??\n", "\n", "# Determine the accuracy of the model\n", "score = rf.score(X_test_sc, y_test)\n", "class_label=['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',\n", "                 'DailyWash', 'DailyRinse','DailySpin']\n", "\n", "rf.fit(??, ??)\n", "rf.predict(??)\n", "cm = confusion_matrix(y_test, rf.predict(??), labels=class_label)\n", "#\n", "axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)\n", "axes.set_ylabel('Actual')\n", "axes.set_ylabel('Predication')\n", "tick_marks = np.arange(len(class_label)) + 0.5\n", "axes.set_xticks(tick_marks)\n", "axes.set_xticklabels(class_label, rotation = 90)\n", "axes.set_yticks(tick_marks)\n", "axes.set_yticklabels(class_label, rotation = 0)\n", "axes.set_title('Confusion Matrix')\n", "plt.show()\n", "\n", "print(\"Model Score {}\" .format( score) )\n"]}, {"cell_type": "markdown", "id": "103276b8", "metadata": {}, "source": ["Finally, let's calculate the Accuracy, Precision, Recall and F1 Score metrics for the Random Forest model.\n"]}, {"cell_type": "code", "execution_count": 17, "id": "f98c2f10", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Accuracy: 85.3%, Precision: 87.5%, Recall: 89.9%, F1 Score: 88.3%\n"]}], "source": ["from sklearn.metrics import ??, ??, ??\n", "\n", "rf =??Classifier(n_estimators=??,\n", "                            max_depth=9,\n", "                            random_state=??)\n", "rf.fit(??, ??)\n", "\n", "accuracy = accuracy_score(??, y_pred_test)\n", "precision = precision_score(??, y_pred_test, average='macro')\n", "recall = recall_score(??, y_pred_test, average='macro')\n", "f1 = f1_score(??, y_pred_test, average='macro')\n", "\n", "print (\"Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%\".format(100*accuracy, 100* precision, 100*recall, 100*f1))\n"]}, {"cell_type": "markdown", "id": "506f2b45", "metadata": {}, "source": ["Finally, are the 4 metrics calculated for random forest better or worst than KNN and Decision Tree models? Which model will you propose for your stakeholder to adopt? Justify your reasons for choosing it?\n"]}, {"cell_type": "markdown", "id": "b1038637", "metadata": {}, "source": ["Your response here ... \n"]}, {"cell_type": "markdown", "id": "a9f0c233", "metadata": {}, "source": ["You may save your random forest model for future use. \n"]}, {"cell_type": "code", "execution_count": 18, "id": "449e21b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model Saved\n"]}], "source": ["import pickle as pk\n", "\n", "model_filename = ?? + \"/model/rf.mdl\"\n", "with open(model_filename, \"wb\") as file:\n", "    pk.dump(rf, file)\n", "print(\"Model Saved\")\n"]}, {"cell_type": "markdown", "id": "f5b13454", "metadata": {}, "source": ["***"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}