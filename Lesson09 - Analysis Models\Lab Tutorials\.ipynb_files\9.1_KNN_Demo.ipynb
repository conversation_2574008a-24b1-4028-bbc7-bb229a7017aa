{"cells": [{"cell_type": "markdown", "id": "2130b04d", "metadata": {"id": "2130b04d"}, "source": ["# Lab Demonstration - KNN\n", "****\n", "\n", "K Nearest Neighbour (KNN) is a very simple, easy to understand, versatile and one of the topmost machine learning algorithms. KNN used in the variety of applications such as finance, healthcare, political science, handwriting detection, image recognition and video recognition. In finance, financial institutes will predict the credit rating of customers. In loan disbursement, banking institutes will predict whether the loan is safe or risky. In political science, classifying potential voters in two classes will vote or won’t vote. KNN algorithm used for both classification and regression problems. KNN algorithm based on feature similarity approach.\n", "\n", "KNN is a non-parametric and lazy learning algorithm. Non-parametric means there is no assumption for underlying data distribution. In other words, the model structure determined from the dataset. This will be very helpful in practice where most of the real world datasets do not follow mathematical/theoretical assumptions. Lazy algorithm means it does not need any training data points for model generation. All training data are used in the testing phase. This makes training faster and testing phase slower and costlier. Costly testing phase means time and memory. In the worst case, KNN needs more time to scan all data points and scanning all data points will require more memory for storing training data.\n", "\n", "![knn_1.png](attachment:knn_1.png)"]}, {"cell_type": "markdown", "id": "68607e12", "metadata": {"id": "68607e12"}, "source": ["## How <PERSON><PERSON><PERSON> work?\n", "\n", "In KNN, K is the number of nearest neighbours. The number of neighbours is the core deciding factor. K is generally an odd number if the number of classes is 2. When K=1, then the algorithm is known as the nearest neighbour algorithm. This is the simplest case. Suppose P1 is the point, for which label needs to predict. First, you find the one closest point to P1 and then the label of the nearest point assigned to P1.\n", "\n", "Suppose P1 is the point, for which label needs to predict. First, you find the k closest point to P1 and then classify points by majority vote of its k neighbours. Each object votes for their class and the class with the most votes is taken as the prediction. For finding closest similar points, you find the distance between points using distance measures such as Euclidean distance, Hamming distance, Manhattan distance and Minkowski distance. KNN has the following basic steps:\n", "\n", "1. Calculate distance\n", "2. Find closest neighbors\n", "3. Vote for labels\n", "\n", "![knn_2.png](attachment:knn_2.png)\n", "\n", "Let's show how a KNN classifier work.\n", "\n", "***"]}, {"cell_type": "markdown", "id": "d1c2d466", "metadata": {"id": "d1c2d466"}, "source": ["\n", "## KNN Classifier\n", "\n", "### Defining Dataset\n", "\n", "Let's first create a dataset. Here you need two kinds of attributes or columns in your data: Feature and label. The reason for two types of column is \"supervised nature of KNN algorithm\".\n", "\n", "Let's create a dataset with two features (in blue ) and one label (in orange) that contain a record of weather, temperature and the decision to play tennis from the past 14 days. This dataset will be used to build a model to predict whether the weather and temperature today is suitable for you to play a tennis match outdoor.\n", "\n", "![table-3.png](attachment:table-3.png)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3a251b35", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "3a251b35", "executionInfo": {"status": "ok", "timestamp": 1749716427608, "user_tz": -480, "elapsed": 27947, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "332bc7d2-a272-4c5c-a3b9-33a39fdb636c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "ad494130", "metadata": {"id": "ad494130", "executionInfo": {"status": "ok", "timestamp": 1749716434424, "user_tz": -480, "elapsed": 10, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"]}, {"cell_type": "code", "execution_count": 3, "id": "41b6909a", "metadata": {"id": "41b6909a", "executionInfo": {"status": "ok", "timestamp": 1749716438779, "user_tz": -480, "elapsed": 11, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["# First Feature\n", "weather = ['<PERSON>','<PERSON>','Overcast','Rainy','Rainy','Rainy','Overcast','<PERSON>','<PERSON>',\n", "'Rainy','<PERSON>','Overcast','Overcast','Rainy']\n", "\n", "# Second Feature\n", "temp = ['Hot','Hot','Hot','Mild','Cool','Cool','Cool','Mild','Cool','Mild','Mild','Mild','Hot','Mild']\n", "\n", "# Label or target varible\n", "tennis = ['No','No','Yes','Yes','Yes','No','Yes','No','Yes','Yes','Yes','Yes','Yes','No']"]}, {"cell_type": "markdown", "id": "8489462d", "metadata": {"id": "8489462d"}, "source": ["### Encoding data columns\n", "\n", "Various machine learning algorithms require numerical input data, so you need to represent categorical columns in a numerical column.\n", "\n", "In order to encode this data, you could map each value to a number. e.g. Overcast:0, <PERSON><PERSON>:1, and <PERSON>:2.\n", "\n", "This process is known as label encoding, and $sklearn$ conveniently will do this for you using $Label Encoder$.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "44316899", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "44316899", "executionInfo": {"status": "ok", "timestamp": 1749716444062, "user_tz": -480, "elapsed": 1426, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "4fb83a1b-26ff-487d-927f-a40be879ac9c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[2 2 0 1 1 1 0 2 2 1 2 0 0 1]\n"]}], "source": ["# Importing preprocessing library\n", "from sklearn import preprocessing\n", "\n", "# Creating a labelEncoder\n", "le = preprocessing.LabelEncoder()\n", "\n", "# Converting string labels into numbers.\n", "weather_encoded = le.fit_transform(weather)\n", "print (weather_encoded)"]}, {"cell_type": "markdown", "id": "a89c0027", "metadata": {"id": "a89c0027"}, "source": ["Here, you have imported preprocessing module and created Label Encoder object. Using this $LabelEncoder$ object, you can fit and transform \"weather\" column into the numeric column. Similarly, you can encode temperature and label into numeric columns.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "2e2d44b1", "metadata": {"id": "2e2d44b1", "executionInfo": {"status": "ok", "timestamp": 1749716451666, "user_tz": -480, "elapsed": 3, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["# Converting string labels into numbers\n", "temp_encoded = le.fit_transform(temp)\n", "label = le.fit_transform(tennis)"]}, {"cell_type": "markdown", "id": "bec433cc", "metadata": {"id": "bec433cc"}, "source": ["### Combining Features\n", "\n", "You can combine multiple columns or features into a single set of data using \"zip\" function.\n"]}, {"cell_type": "code", "execution_count": null, "id": "69aa243e", "metadata": {"id": "69aa243e"}, "outputs": [], "source": ["# Combining weather and temp into a single list of tuples\n", "features = list(zip(weather_encoded, temp_encoded))"]}, {"cell_type": "markdown", "id": "71fb0166", "metadata": {"id": "71fb0166"}, "source": ["### Generating Model\n", "\n", "Let's build KNN classifier model.\n", "\n", "First, import the $KNeighborsClassifier$ module and create KNN classifier object by passing argument 'n_neighors' that represent the number of neighbours (K) in the $KNeighborsClassifier()$ function. Let's set $K=3$.\n", "\n", "Next, fit your model on the train set using $fit()$ before you perform your prediction on the test set with $predict()$.\n"]}, {"cell_type": "code", "execution_count": null, "id": "32f692a3", "metadata": {"id": "32f692a3", "outputId": "178525a9-98b7-425e-c4bf-b71e33ff3887"}, "outputs": [{"data": {"text/plain": ["KNeighborsClassifier(n_neighbors=3)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.neighbors import KNeighborsClassifier\n", "\n", "model = KNeighborsClassifier(n_neighbors=3)\n", "\n", "# Train the model using the training sets\n", "model.fit(features, label)"]}, {"cell_type": "markdown", "id": "426d03b8", "metadata": {"id": "426d03b8"}, "source": ["Assume today's weather is overcast and the temperature is mild, let's test the outcome of the KNN model to predict wether you should play tennis today."]}, {"cell_type": "code", "execution_count": null, "id": "4d82d680", "metadata": {"id": "4d82d680", "outputId": "0c00d8ac-c44b-459c-deba-d746fce43af6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1]\n"]}], "source": ["# Predict the Output\n", "predicted = model.predict([[0, 2]]) # 0:Overcast, 2:Mild\n", "print (predicted)"]}, {"cell_type": "markdown", "id": "755aa3eb", "metadata": {"id": "755aa3eb"}, "source": ["In the above demonstration, you have given input [0, 2], where 0 means Overcast weather and 2 means Mild temperature. The KNN model has predicts [1], which means today's weather and temperature is suitable for you to play tennis.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}