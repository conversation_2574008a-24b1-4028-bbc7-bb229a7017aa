https://ecomagazine.com/news/fisheries-aquaculture/artificial-intelligence-and-its-aquaculture-applications/

Artificial Intelligence and Its Aquaculture Applications
人工智能及其在水产养殖中的应用
4 February 2025 2025年2月4日

Auto-feeders at a shrimp pond. (Image credit: <PERSON>)
一个虾塘的自动喂食器。（图片来源：费尔南多·韦尔塔）
Aquaculture continues to evolve as a vital component of modern agriculture and environmental management through the facilitation of artificial intelligence of things (AIoT), which integrates artificial intelligence (AI) and the Internet of Things (IoT) to enhance fish farming practices. For instance, digital twins enable real-time monitoring and decision-making, sustainable IoT solutions reduce resource wastage and promote environmental resilience, IoT-based systems improve disease detection and health management, and AIoT-driven water quality inspection systems enhance operational efficiency and scalability.
随着人工智能物联网（AIoT）的推动，水产养殖作为现代农业和环境管理的重要组成部分不断发展。人工智能物联网融合了人工智能（AI）和物联网（IoT），以改进养鱼实践。例如，数字孪生技术实现了实时监测与决策，可持续的物联网解决方案减少了资源浪费并提升了环境适应能力，基于物联网的系统改善了疾病检测与健康管理，而由人工智能物联网驱动的水质检测系统则提高了运营效率与可扩展性。
The development of IoT devices, including sensors, cameras, and monitoring systems, has revolutionized data collection from remote and dynamic aquaculture environments. These devices provide continuous streams of data on critical parameters—such as water quality, fish behavior, feeding patterns, and environmental conditions—and AI algorithms analyze these data to generate actionable insights that drive real-time decision-making and autonomous operations, thereby reducing human intervention and enabling smarter, more sustainable practices.
物联网设备（包括传感器、摄像头和监测系统）的发展，彻底改变了从远程和动态水产养殖环境中收集数据的方式。这些设备持续提供有关关键参数（如水质、鱼类行为、摄食模式和环境条件）的数据流，人工智能算法对这些数据进行分析，以生成可行的见解，从而推动实时决策和自主操作，进而减少人为干预，实现更智能、更可持续的实践。

The foundational IoT infrastructure in aquaculture consists of data-gathering devices, connectivity gateways, and cloud platforms for data storage and processing. This infrastructure supports real-time monitoring and analysis of environmental parameters like temperature, salinity, pH, and dissolved oxygen; factors critical for maintaining optimal aquatic conditions. Cloud computing enables the parallel processing and scalability needed to handle large-scale datasets efficiently, providing a robust platform for integrating AI capabilities into aquaculture systems.
水产养殖中的基础物联网基础设施由数据采集设备、连接网关以及用于数据存储和处理的云平台组成。该基础设施支持对温度、盐度、酸碱度和溶解氧等环境参数进行实时监测与分析，这些因素对于维持最佳水产生长条件至关重要。云计算实现了高效处理大规模数据集所需的并行处理和可扩展性，为将人工智能功能集成到水产养殖系统中提供了一个强大的平台。

AIoT applications in aquaculture span a broad spectrum of innovations, including smart feeding systems, water quality management, disease detection, fish biomass estimation, fish behavior monitoring, organism counting, species segmentation and classification, breeding and growth estimation, individual fish tracking, automation, and robotics. For instance, IoT-enabled smart feeding systems utilize data from sensors and underwater cameras to monitor fish behavior and optimize feeding schedules and quantities. The AI models applied in this study analyze the collected data to ensure fish are fed adequately while minimizing waste, improving growth rates, and reducing environmental impact.
人工智能物联网（AIoT）在水产养殖中的应用涵盖了广泛的创新领域，包括智能投喂系统、水质管理、疾病检测、鱼类生物量估算、鱼类行为监测、生物计数、物种分割与分类、繁殖与生长估算、个体鱼类追踪、自动化以及机器人技术。例如，基于物联网的智能投喂系统利用传感器和水下摄像头的数据来监测鱼类行为，并优化投喂计划和投喂量。本研究中应用的人工智能模型会分析收集到的数据，以确保鱼类得到充足的投喂，同时将浪费降至最低，提高生长速度，并减少对环境的影响。

The integration of AI and IoT, known as artificial intelligence of things (AIoT), is driving significant advancements in the aquaculture industry, offering solutions to longstanding challenges related to operational efficiency, sustainability, and productivity.
人工智能与物联网的融合，即所谓的“人工智能物联网”（AIoT），正在推动水产养殖业取得重大进展，为解决长期以来与运营效率、可持续性和生产力相关的挑战提供解决方案。

This article discusses a review that comprehensively analyzed advancements in AIoT applications within aquaculture, drawing from 215 research papers published between 2012 and 2024. The papers are systematically categorized into ten core application areas: smart feeding systems, water quality management, disease detection, fish biomass estimation, fish behavior monitoring, organism counting, species segmentation and classification, breeding and growth estimation, individual fish tracking, and automation and robotics.
本文讨论了一篇综述，该综述从2012年至2024年发表的215篇研究论文中，全面分析了水产养殖中人工智能物联网应用的进展。这些论文被系统地分为十个核心应用领域：智能投喂系统、水质管理、疾病检测、鱼类生物量估计、鱼类行为监测、生物计数、物种分割与分类、繁殖与生长估计、个体鱼类追踪，以及自动化与机器人技术。

The review also explores AIoT advantages and adoption challenges, such as high implementation costs, data privacy concerns, and the need for scalable and adaptable AI models across diverse environments. It also highlights future AIoT trends, including the development of hybrid models, scalability solutions, and AIoT’s role in promoting sustainable aquaculture practices. By providing an in-depth analysis of the long-term potential of AIoT, this review paper highlights its transformative role in advancing global aquaculture practices and sustainability.
该综述还探讨了人工智能物联网（AIoT）的优势以及采用过程中面临的挑战，如实施成本高、数据隐私问题，以及在不同环境中对可扩展且适应性强的人工智能模型的需求。它还强调了人工智能物联网未来的发展趋势，包括混合模型的开发、可扩展性解决方案，以及人工智能物联网在促进可持续水产养殖实践中的作用。通过对人工智能物联网长期潜力的深入分析，这篇综述文章突出了其在推动全球水产养殖实践和可持续发展方面的变革性作用。


Conceptual framework of AIoT in aquaculture. (Image credit: Global Seafood Alliance)
水产养殖中物联网的概念框架。（图片来源：全球海鲜联盟）
AIoT Applications in Aquaculture 人工智能物联网在水产养殖中的应用
Integrating AIoT into aquaculture is revolutionizing farm management by merging real-time IoT sensor data collection with advanced AI-driven analytics. This synergy is enhancing efficiency, sustainability, and productivity in aquaculture operations.
将人工智能物联网（AIoT）集成到水产养殖中，通过将实时物联网传感器数据收集与先进的人工智能驱动分析相结合，正在彻底改变养殖场管理方式。这种协同效应正在提高水产养殖作业的效率、可持续性和生产力。

Smart Feeding Systems 智能喂食系统
Smart feeding systems represent a transformative application area in aquaculture. These applications monitor fish feeding activity and environmental parameters to automate feeding, ensuring aquatic organisms receive the appropriate amount of food at optimal times. Research to date has provided a solid foundation for smart feeding systems, exploring various methods to enhance feeding strategies and efficiency. These methods include optimization of feeding frequency and nutrient distribution using various models to refine feeding schedules and improve growth rates, automated detection of feeding behavior using advanced computer vision techniques, real-time feed detection systems, acoustic-based monitoring to correlate feeding sound intensity with hunger levels, and classify feeding intensity using models,
智能投喂系统是水产养殖领域中一个具有变革性的应用领域。这些应用通过监测鱼类的摄食活动和环境参数来实现投喂自动化，确保水生生物在最佳时间获得适量的食物。迄今为止的研究为智能投喂系统奠定了坚实的基础，探索了各种提高投喂策略和效率的方法。这些方法包括利用各种模型优化投喂频率和营养分布，以完善投喂计划并提高生长速度；使用先进的计算机视觉技术自动检测摄食行为；采用实时饲料检测系统；通过基于声学的监测将摄食声音强度与饥饿程度相关联，并使用模型对摄食强度进行分类。

AI-enhanced precision feeding systems for real-time feeding adjustments and integration of multimodal data to provide a comprehensive understanding of feeding behavior.
人工智能增强的精准投喂系统，用于实时调整投喂，并整合多模态数据，以全面了解投喂行为。

Water Quality Management 水质管理
Maintaining optimal water quality is fundamental for sustainable and productive aquaculture, as aquatic species’ health, growth, and welfare are directly influenced by environmental conditions. Deviations in parameters like dissolved oxygen, pH, temperature, and ammonia levels can significantly affect fish health, impacting growth rates and disease susceptibility. Integrating IoT sensors and AI-based predictive analytics has proven essential in modern aquaculture, enabling continuous, real-time monitoring and early intervention to prevent adverse conditions.
维持最佳水质是可持续和高产水产养殖的基础，因为水生物种的健康、生长和生存状况直接受到环境条件的影响。溶解氧、酸碱度、温度和氨含量等参数的偏差会显著影响鱼类健康，进而影响生长速度和疾病易感性。事实证明，在现代水产养殖中，整合物联网传感器和基于人工智能的预测分析至关重要，能够实现持续实时监测并提前干预，以防止出现不利状况。

Studies have demonstrated that proactive monitoring systems can effectively forecast critical events in aquaculture, for instance, for predicting critical aquaculture events such as oxygen depletion and water quality deterioration; the ability to predict algal blooms and maintain environmental stability through AI-driven monitoring; the use of genetic algorithms combined with ensemble learning to optimize dissolved oxygen predictions; and model for accurate marine temperature prediction, which indirectly supports forecasting algal blooms and related events.
研究表明，主动监测系统能够有效地预测水产养殖中的关键事件，例如，预测诸如氧气耗尽和水质恶化等关键水产养殖事件；通过人工智能驱动的监测来预测藻类大量繁殖并维持环境稳定性的能力；利用遗传算法结合集成学习来优化溶解氧预测；以及用于准确预测海洋温度的模型，这间接有助于预测藻类大量繁殖及相关事件。

Disease Detection and Classification 疾病检测与分类
Early disease detection and classification are vital for effective control and management of disease outbreaks in aquaculture farms. This ultimately contributes to the health and productivity of fish and shrimp stocks. Disease outbreaks not only affect the welfare of cultured species but also lead to significant economic losses in fish farming practices. Advancements in biosensor technology contribute to disease prevention by providing precise pathogen detection capabilities. Relevant areas include computer vision-based disease detection, water quality-linked disease prediction, cross-modal and zero-shot learning for disease identification, ensemble and hybrid models for disease classification, adaptive neural fuzzy systems, mobile and IOT-enabled disease monitoring systems, and biosensors for pathogen detection.
早期疾病检测和分类对于水产养殖场疾病暴发的有效控制和管理至关重要。这最终有助于鱼类和虾类种群的健康和生产力。疾病暴发不仅影响养殖物种的健康，还会在养鱼实践中造成重大经济损失。生物传感器技术的进步通过提供精确的病原体检测能力，有助于疾病预防。相关领域包括基于计算机视觉的疾病检测、与水质相关的疾病预测、用于疾病识别的跨模态和零样本学习、用于疾病分类的集成和混合模型、自适应神经模糊系统、基于移动和物联网的疾病监测系统，以及用于病原体检测的生物传感器。

Fish Biomass Estimation 鱼类生物量估算
Fish biomass estimation is critical in optimizing aquaculture operations by enabling accurate assessments of fish health, growth, and population density. Reliable biomass estimation facilitates efficient feeding routines, reduces waste accumulation, and promotes sustainable fish farming practices. Modern advancements in machine learning, computer vision, sonar, and smart scale technologies have enabled non-invasive, real-time biomass estimation in aquaculture, overcoming traditional challenges associated with manual measurements and stress-induced inaccuracies.
鱼类生物量估算对于优化水产养殖作业至关重要，因为它能够准确评估鱼类健康状况、生长情况和种群密度。可靠的生物量估算有助于实现高效的投喂计划，减少废物积累，并推动可持续的养鱼实践。机器学习、计算机视觉、声纳和智能称重技术的最新进展，已实现了水产养殖中无创、实时的生物量估算，克服了传统手动测量和因应激导致的不准确等相关挑战。

Fish Behavior Detection 鱼类行为检测
Monitoring fish behavior is critical for maintaining fish health and welfare and achieving optimal growth in aquaculture systems. Behavioral changes often serve as early indicators of stress, disease, satiety or hunger, and adverse environmental conditions, providing valuable insights for timely interventions. Recent advancements in AI, IoT, acoustic monitoring, and computer vision have revolutionized behavior detection, enabling non-invasive, real-time monitoring even in complex aquaculture environments. These technologies have demonstrated significant improvements in accuracy and reliability, as evidenced by various studies.
监测鱼类行为对于维持鱼类健康和福利以及在水产养殖系统中实现最佳生长至关重要。行为变化通常是压力、疾病、饱腹感或饥饿以及不利环境条件的早期指标，为及时干预提供了有价值的见解。人工智能、物联网、声学监测和计算机视觉方面的最新进展彻底改变了行为检测，即使在复杂的水产养殖环境中也能够进行非侵入性的实时监测。正如各项研究所证明的，这些技术在准确性和可靠性方面有了显著提高。

Counting Aquaculture Organisms 水产养殖生物计数
Accurate counting of organisms in aquaculture is vital for managing stock, monitoring health, and optimizing feeding. Traditionally, this task has been labor-intensive, invasive, and prone to inaccuracies, especially in high-density environments. Recent developments in AI, computer vision, and sensor technologies have made automated, accurate counting feasible for a variety of aquaculture species, including fish, shrimp, and holothurians. Existing studies have reported considerable success in counting aquaculture organisms using smartphone-based deep learning models for counting shrimp, applying computer vision for behavioral study and fish counting in controlled environments, and using an echosounder-based algorithm for estimating fish populations in farming nets.
在水产养殖中，准确统计生物数量对于管理种群、监测健康状况和优化投喂至关重要。传统上，这项任务劳动强度大、具有侵入性，而且容易出现误差，尤其是在高密度环境中。人工智能、计算机视觉和传感器技术的最新发展，使得对包括鱼类、虾类和海参等多种水产养殖物种进行自动化、准确的数量统计成为可能。现有研究表明，使用基于智能手机的深度学习模型统计虾的数量、在受控环境中应用计算机视觉进行行为研究和鱼类数量统计，以及使用基于回声测深仪的算法估算养殖网中的鱼群数量，在统计水产养殖生物数量方面取得了显著成功。

Segmentation, Detection, and Classification of Aquaculture Species
水产养殖物种的分割、检测与分类
The segmentation, detection, and classification of aquaculture species are critical tasks for effective aquaculture monitoring and biodiversity conservation. Advances in AI and computer vision have introduced robust solutions for these tasks, even in challenging underwater conditions. This area involves recent research studies that address the complexity of recognizing aquaculture species and marine structures in a variety of environments.
水产养殖物种的分割、检测和分类是有效进行水产养殖监测和生物多样性保护的关键任务。人工智能和计算机视觉技术的进步为这些任务提供了强大的解决方案，即使在具有挑战性的水下环境中也是如此。该领域涉及近期的研究，旨在解决在各种环境中识别水产养殖物种和海洋结构的复杂性问题。

Breeding and Growth Estimation 繁殖与生长预估
Breeding and growth estimation in aquaculture is essential for optimizing fish feeding, fish health evaluation, reproductive success, and environmental safety. Recent studies have focused on developing automated and accurate techniques to estimate growth parameters and monitor breeding maturity, utilizing advanced AI models, computer vision, and stereo vision technology. This area includes applications like automated breeding and spawning detection, fish population estimation using echosounders and digital twins, growth estimation, and environmental monitoring for growth and health.
在水产养殖中，繁殖与生长评估对于优化鱼类喂养、鱼体健康评估、繁殖成功率以及环境安全至关重要。近期研究主要集中在利用先进的人工智能模型、计算机视觉和立体视觉技术，开发自动化且准确的技术来估计生长参数并监测繁殖成熟度。这一领域的应用包括自动化繁殖与产卵检测、使用回声测深仪和数字孪生技术进行鱼类种群评估、生长评估以及针对生长和健康状况的环境监测。

Fish Tracking and Individuality 鱼类追踪与个体识别
Critical for monitoring fish behavior, health, feeding patterns, breeding, and population dynamics in both controlled and natural environments. Recent advancements in AIoT have enabled more accurate and scalable solutions for individual and group tracking and behavior analysis in complex aquatic environments. Areas of interest include individual fish identification, multi-fish tracking in controlled and unconstrained environments, activity segmentation and tracking in sonar and echogram data, fish group activity and behavior recognition, tracking fish around marine structures and moving cameras, and individuality and behavior tracking for aquaculture monitoring.
这对于监测受控环境和自然环境中的鱼类行为、健康状况、摄食模式、繁殖及种群动态至关重要。物联网与人工智能（AIoT）的最新进展为复杂水生环境中的个体和群体追踪及行为分析提供了更准确、更具扩展性的解决方案。研究关注领域包括个体鱼类识别、受控及非受控环境中的多鱼追踪、声纳和回波图数据中的活动分割与追踪、鱼群活动和行为识别、在海洋结构物周围及移动摄像头下追踪鱼类，以及用于水产养殖监测的个体特征与行为追踪。

Automation and Robotics in Aquaculture
水产养殖中的自动化与机器人技术
Automation and robotics in aquaculture employ autonomous underwater vehicles (AUVs) and reinforcement learning algorithms to automate tasks such as tank cleaning, fish inspection, and feed delivery. While robotics improves operational efficiency, limitations like battery life, connectivity, and implementation costs hinder scalability. These diverse applications address a wide range of challenges for sustainable fish farming, resource management, and environmental conservation and include remote sensing and monitoring systems, robotic and autonomous systems for underwater monitoring, UAV- and AUV-based aquaculture inspections, hybrid and smart monitoring systems, advanced imaging and sensor, technologies, digital twin and predictive modeling, biologically inspired robotics, and specialized aquaculture applications which expand the scope of robotics in aquaculture.
水产养殖中的自动化与机器人技术采用自主水下航行器（AUV）和强化学习算法，实现诸如水池清洁、鱼类检查和饲料投喂等任务的自动化。虽然机器人技术提高了运营效率，但电池续航、连接性和实施成本等限制因素阻碍了其规模扩大。这些多样化的应用解决了可持续养鱼、资源管理和环境保护等一系列挑战，涵盖遥感与监测系统、用于水下监测的机器人和自主系统、基于无人机（UAV）和自主水下航行器的水产养殖检查、混合与智能监测系统、先进成像与传感器技术、数字孪生与预测建模、受生物启发的机器人技术，以及拓展水产养殖中机器人技术应用范围的专业水产养殖应用。


Applications of AIoT in aquaculture. (Image credit: Global Seafood Alliance)
物联网人工智能在水产养殖中的应用。（图片来源：全球海鲜联盟）
Concluding Remarks and Future Work 结束语与未来工作
Integrating AI and IoT contributes to the advancement of aquaculture by effectively addressing critical challenges such as operational inefficiencies, environmental sustainability, disease management, and resource optimization. This review categorizes AIoT applications into ten core areas: smart feeding systems, water quality management, disease detection, fish biomass estimation, fish behavior detection, counting aquaculture organisms, species segmentation and classification, breeding and growth estimation, fish tracking and individuality, and automation and robotics. Each area highlights unique methodologies, key achievements, and potential for future advancements.
将人工智能（AI）与物联网（IoT）相结合，通过有效应对运营效率低下、环境可持续性、疾病管理和资源优化等关键挑战，推动了水产养殖业的发展。本综述将人工智能与物联网的应用分为十个核心领域：智能投喂系统、水质管理、疾病检测、鱼类生物量估算、鱼类行为检测、水产养殖生物计数、物种分割与分类、育种与生长估算、鱼类追踪与个体识别，以及自动化与机器人技术。每个领域都突出了独特的方法、主要成果以及未来发展的潜力。

Among these, smart feeding systems exemplify how AIoT optimizes feeding schedules, minimizes waste, and enhances feed efficiency, significantly improving growth rates. Future research in this area is expected to develop adaptive systems and leverage cloud-based monitoring to ensure scalability and robustness. Similarly, water quality management has progressed with real-time monitoring and predictive adjustments enabled by AIoT, helping to maintain optimal conditions for fish welfare. However, challenges such as scaling across diverse environments and incorporating edge computing solutions for real-time processing remain priorities for future development.
在这些应用中，智能投喂系统就是人工智能物联网如何优化投喂计划、减少浪费并提高饲料效率的典型例子，这显著提高了鱼类的生长速度。预计该领域未来的研究将开发自适应系统，并利用基于云的监测技术，以确保系统的可扩展性和稳定性。同样，借助人工智能物联网实现的实时监测和预测性调整，水质管理也取得了进展，有助于维持鱼类健康生长的最佳条件。然而，诸如在不同环境中进行扩展，以及整合边缘计算解决方案以进行实时处理等挑战，仍是未来发展的重点。

In disease detection and prevention, AI-driven biosensors and IoT tools enable early detection of health issues, reducing mortality rates and improving overall fish health. Nonetheless, the lack of robust datasets and generalized models limits cross-species applicability, requiring further research. Fish biomass estimation, a critical component for yield forecasting, has been improved through computer vision and acoustic analysis techniques. Advancing this area will require refining multimodal systems for varied environments and enhancing real-time capabilities. AIoT also plays a vital role in the detection of fish behavior, where models analyze patterns indicative of stress, disease, or social dynamics.
在疾病检测与预防方面，人工智能驱动的生物传感器和物联网工具能够实现健康问题的早期检测，降低死亡率并提升鱼类整体健康状况。尽管如此，缺乏强大的数据集和通用模型限制了跨物种的适用性，这需要进一步研究。鱼类生物量估计是产量预测的关键组成部分，已通过计算机视觉和声学分析技术得到改进。要推动这一领域的发展，需要针对不同环境优化多模态系统并增强实时处理能力。人工智能物联网在鱼类行为检测方面也发挥着至关重要的作用，相关模型可以分析出表明鱼类处于应激、患病或反映其社会动态的行为模式。

Despite notable progress, challenges such as environmental variability, noise interference, and species-specific behaviors necessitate the development of more adaptable and species-diverse models. Similarly, automated counting methods for aquaculture organisms streamline management but face issues such as occlusion and environmental variability. These methods need further refinement to ensure broad applicability and versatility.
尽管取得了显著进展，但诸如环境变化、噪声干扰和物种特定行为等挑战，使得开发更具适应性和物种多样性的模型成为必要。同样，水产养殖生物的自动计数方法虽然简化了管理工作，但面临着遮挡和环境变化等问题。这些方法需要进一步完善，以确保广泛的适用性和通用性。

Species segmentation and classification showcase the potential of advanced AI techniques in distinguishing species under complex conditions. Yet, ensuring adaptability to diverse environments and integrating low-power, real-time deployment solutions remains a challenge. In the context of breeding and growth estimation, models assessing fish maturity and growth rates are advancing breeding efficiency. Expanding these models to accommodate various species and integrating continuous real-time data are critical steps for more robust monitoring systems.
物种分割与分类展示了先进人工智能技术在复杂条件下区分物种的潜力。然而，确保对不同环境的适应性以及整合低功耗实时部署解决方案仍然是一项挑战。在育种和生长评估方面，评估鱼类成熟度和生长率的模型正在提高育种效率。将这些模型扩展以适应各种物种，并整合连续实时数据，是建立更强大监测系统的关键步骤。

Fish tracking and individuality monitoring provide valuable insights into behavior and the effects of water quality, but challenges persist in high-density and diverse aquaculture environments. Integrating multiple data sources could improve comprehensiveness and precision. Finally, automation and robotics are revolutionizing labor-intensive tasks, especially in offshore or remote locations, by enabling real-time monitoring and execution. Future advancements should focus on multimodal sensors, enhanced connectivity, and efficient power management to improve resilience and operational efficiency.
鱼类追踪和个体监测为了解鱼类行为以及水质影响提供了宝贵的见解，但在高密度和多样化的水产养殖环境中，挑战依然存在。整合多个数据源可以提高全面性和精确性。最后，自动化和机器人技术正在革新劳动密集型任务，特别是在近海或偏远地区，实现实时监测和执行。未来的进展应聚焦于多模态传感器、增强的连接性以及高效的电源管理，以提高适应能力和运营效率。

While the potential of AIoT in aquaculture is transformative, several limitations remain. High initial investment costs, the complexity of data infrastructure, and the demand for technical expertise create barriers, particularly for smaller operators. Concerns about data privacy and cybersecurity in interconnected systems pose significant challenges. Many AI models trained for specific species or environments lack the flexibility for broader applications, underscoring the need for adaptive AI solutions.
虽然人工智能物联网在水产养殖中的潜力具有变革性，但仍存在一些局限性。高昂的初始投资成本、数据基础设施的复杂性以及对专业技术知识的需求，都构成了障碍，对小型养殖经营者来说尤其如此。对互联系统中数据隐私和网络安全的担忧带来了重大挑战。许多针对特定物种或环境训练的人工智能模型缺乏广泛应用的灵活性，这凸显了对适应性人工智能解决方案的需求。

Observations from this review suggest that AIoT adoption thrives in environments that prioritize real-time monitoring, adaptive response mechanisms, and scalable infrastructure. Large-scale operations benefit from economies of scale, while smaller operators often face financial and technical hurdles. Bridging this gap will require cost-effective solutions, robust-edge computing systems, and accessible training resources for personnel.
本次审查的观察结果表明，在重视实时监测、自适应响应机制和可扩展基础设施的环境中，人工智能物联网（AIoT）的应用更为成功。大规模运营得益于规模经济，而小型运营商则常常面临资金和技术障碍。要弥合这一差距，需要有具成本效益的解决方案、强大的边缘计算系统，以及为人员提供易于获取的培训资源。