[{"id": "8c3ab9b967c33848", "type": "function", "z": "85039718.8a68d8", "name": "Send data to ThingSpeak", "func": "var output =msg.payload.split(\",\");\nvar ph=parseFloat(output[0]);\nvar ec=parseFloat(output[1]);\nvar temp=parseFloat(output[2]);\nvar result={\n    ph:ph,\n    ec:ec,\n    temp:temp\n};\nmsg.payload=result;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 310, "y": 500, "wires": [["1c3bf6b18a622b6c", "23f187442a86c3e9"]]}]