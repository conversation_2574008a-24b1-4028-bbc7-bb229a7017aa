Red Node Settings

Function node

var output = msg.payload.split(",");

var ph = parseFloat(output[0]);
var ec = parseFloat(output[1]);
var temp = parseFloat(output[2]);
var msg1 = {payload:ph};
var msg2 = {payload:ec};
var msg3 = {payload:temp};

return [msg1, msg2, msg3];

-----------------------------

Send data to ThinkSpeak

var output =msg.payload.split(",");
var ph=parseFloat(output[0]);
var ec=parseFloat(output[1]);
var temp=parseFloat(output[2]);
var result={
    ph:ph,
    ec:ec,
    temp:temp
};
msg.payload=result;
return msg;

-----------------------------

ThinkSpeak

