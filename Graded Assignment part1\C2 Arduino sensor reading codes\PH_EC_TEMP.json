[{"id": "28309c53.00fd54", "type": "function", "z": "85039718.8a68d8", "name": "PH, EC, Temp", "func": "var output = msg.payload.split(\",\");\n\nvar ph = parseFloat(output[0]);\nvar ec = parseFloat(output[1]);\nvar temp = parseFloat(output[2]);\nvar msg1 = {payload:ph};\nvar msg2 = {payload:ec};\nvar msg3 = {payload:temp};\n\nreturn [msg1, msg2, msg3];\n", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 280, "y": 100, "wires": [["c7a7d21d.7865", "e43017569f34ec52", "a440037a67b3f12b", "af63a9dbb9d50b8c"], ["447a0f6b.f0a3e", "b9edb14882d68789", "172bb9da1ad71049", "30d5d186d61ac574"], ["28006808.5c7468", "ce3d2eefbe9d246b", "66ad245e672e86c8", "739ffb96eddb5b79"]]}]