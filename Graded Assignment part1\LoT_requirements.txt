Graded Assignment (GA) Section A: IoT requirements:

A. IoT System Architecture deployed at Smith Marine
- The overall architecture of the IoT system
- The types of sensors used
- How data is collected
- How data is transmitted
- How data is stored
- The cloud-based platforms for monitoring and analysis.

B. Photo of the IoT System
-The deployed hardware setup
-The sensor placements
- The microcontrollers
- The connectivity modules

C. Arduino Source Code
- The Arduino code used to interface with the sensors
- The code responsible for sensor readings
- The code responsible for data formatting
- The code responsible for communication with external platforms

D. Screenshot of the Node-RED Flow
- The Node-RED flow and explain its logic
- How the flow handles data input
- How the flow handles processing
- How the flow handles output to the chosen platforms (e.g., dashboards or APIs).

E. Node-RED and ThingSpeak Dashboards
- Screenshots of the dashboards
- The data visualisation components used
- How these dashboards can assist Smith Marine in monitoring environmental conditions
- How these dashboards can managing Smith Marine seafood livestock more effectively

