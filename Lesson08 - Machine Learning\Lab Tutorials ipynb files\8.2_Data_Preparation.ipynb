{"cells": [{"cell_type": "markdown", "id": "81d187d1", "metadata": {"id": "81d187d1"}, "source": ["# Data Preparation for Laudromat Use Case\n", "\n", "***\n", "\n", "## Features description from the $CleandDataV20210515.csv$ file:\n", "\n", "**index_col** time step for the washing cycle\n", "\n", "**avC:** average current\n", "\n", "**avP:** average power\n", "\n", "**avR:** average resistant\n", "\n", "**maxC:** maximum current\n", "\n", "**maxP:** maximum power\n", "\n", "**sdC:** standard deviation for current\n", "\n", "**sdP:** standard deviation for power\n", "\n", "**stdCR:** standard deviation for resistant\n", "\n", "**stdCP:** standard deviation for power\n", "\n", "**AvRR:** average relative resistance to previous resistance reading\n", "\n", "**mode:** positive class: Daily Wash | negative class: Not Daily Wash\n"]}, {"cell_type": "code", "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "f9U61-m_NjNn", "executionInfo": {"status": "ok", "timestamp": 1749716573608, "user_tz": -480, "elapsed": 2574, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "3b7cb7b0-aafa-402a-9b01-9135ed6c5ecd"}, "id": "f9U61-m_NjNn", "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}]}, {"cell_type": "code", "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"], "metadata": {"id": "ACLRC7CTNmTN", "executionInfo": {"status": "ok", "timestamp": 1749716575338, "user_tz": -480, "elapsed": 5, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "id": "ACLRC7CTNmTN", "execution_count": 9, "outputs": []}, {"cell_type": "code", "execution_count": 10, "id": "7a4a8a70", "metadata": {"id": "7a4a8a70", "executionInfo": {"status": "ok", "timestamp": 1749716577162, "user_tz": -480, "elapsed": 3, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["# Import necessary Python libraries\n", "import numpy as np\n", "import pandas as pd\n", "import pickle as pk\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "a770d865", "metadata": {"id": "a770d865"}, "source": ["### Data Preparation Process"]}, {"cell_type": "code", "execution_count": 11, "id": "771285eb", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "771285eb", "executionInfo": {"status": "ok", "timestamp": 1749716579817, "user_tz": -480, "elapsed": 457, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "59d0197c-9f5a-44d2-dd8f-3ef0260e8767"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["     index_col       avC         avP       sdC         sdP          avR  maxC  \\\n", "432          3  3.595600  792.930000  3.332669  786.913383   399.232039  7.23   \n", "22           9  1.616815  329.370370  2.485054  583.668067   637.157143  1.76   \n", "620         14  1.305465  254.702326  2.232888  529.072646   604.123431  0.66   \n", "963          0  0.284667   34.533333  0.227744   33.000244  1429.358887  0.77   \n", "188          9  1.484593  298.003704  2.357156  558.706609   623.840883  1.84   \n", "..         ...       ...         ...       ...         ...          ...   ...   \n", "360         21  0.906095  167.071429  1.721403  405.103331   871.333092  0.09   \n", "466         15  1.292000  256.215556  2.277856  534.821437   658.162380  1.30   \n", "299          0  2.612000  574.333333  3.216506  759.213038   753.064179  7.25   \n", "493         20  1.080317  202.135000  1.919027  450.266332   678.029294  2.49   \n", "527          9  1.641481  339.711111  2.584596  619.005346   585.429776  0.59   \n", "\n", "     maxP     stdCR       stdCP         AvRR  \n", "432  1642  3.382349  805.313577   362.408577  \n", "22    271  0.587681   92.810374   449.344412  \n", "620    69  0.141624   18.216104   682.374211  \n", "963   102  0.227744   33.000244  1429.358887  \n", "188   286  0.601370   96.551553   475.380790  \n", "..    ...       ...         ...          ...  \n", "360     1  0.000000    0.000000  2628.307407  \n", "466   195  0.325882   50.023153   638.702682  \n", "299  1666  3.216506  759.213038   753.064179  \n", "493   395  0.630406  102.162728   226.295845  \n", "527    75  0.109605   15.296908   559.013457  \n", "\n", "[773 rows x 11 columns]\n", "432     CottonWash\n", "22      DailyRinse\n", "620    CottonRinse\n", "963    BeddingWash\n", "188     DailyRinse\n", "          ...     \n", "360     CottonSpin\n", "466    CottonRinse\n", "299     CottonWash\n", "493     CottonSpin\n", "527     CottonWash\n", "Name: mode, Length: 773, dtype: object\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/numpy/_core/fromnumeric.py:3800: FutureWarning: The behavior of DataFrame.std with axis=None is deprecated, in a future version this will reduce over both axes and return a scalar. To retain the old behavior, pass axis=0 (or do not pass axis)\n", "  return std(axis=axis, dtype=dtype, out=out, ddof=ddof, **kwargs)\n"]}], "source": ["# Import necessary scikit-learn libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# Load the dataset\n", "data_path  = ROOT_DIR + '/data/CleandDataV20210515.csv'\n", "currentdf  = pd.read_csv(data_path)\n", "\n", "x = currentdf.iloc[:, 2:]\n", "y = currentdf.iloc[:, 1]\n", "\n", "# Display the full dataset\n", "#print (x)\n", "\n", "# Splitting dataset into 75% for training and 25% for testing\n", "X_train, X_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=2)\n", "\n", "# Display the features and label from the training set\n", "print (X_train)\n", "print (y_train)\n", "\n", "# Standardize dataset\n", "mean = np.mean(X_train)\n", "stddev = np.std(X_train)\n", "X_train_sc = (X_train - mean) / stddev\n", "X_test_sc = (X_test - mean) / stddev\n"]}, {"cell_type": "markdown", "id": "2051d116", "metadata": {"id": "2051d116"}, "source": ["We shall train some predictive models with this dataset in the next lesson...\n", "\n", "***"]}, {"cell_type": "markdown", "id": "fc84f0d3", "metadata": {"id": "fc84f0d3"}, "source": ["\n", "\n", "***"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}