[{"id": "85039718.8a68d8", "type": "tab", "label": "Flow 1", "disabled": false, "info": ""}, {"id": "8e3887a.09b4278", "type": "serial-port", "serialport": "/dev/ttyACM0", "serialbaud": "115200", "databits": "8", "parity": "none", "stopbits": "1", "waitfor": "", "dtr": "none", "rts": "none", "cts": "none", "dsr": "none", "newline": "\\n", "bin": "false", "out": "char", "addchar": "", "responsetimeout": "10000"}, {"id": "6a878cef.7a1294", "type": "ui_tab", "name": "Water Monitoring System", "icon": "Gauge", "disabled": false, "hidden": false}, {"id": "9e58b18a.abbf9", "type": "ui_base", "theme": {"name": "theme-light", "lightTheme": {"default": "#0094CE", "baseColor": "#0094CE", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif", "edited": true, "reset": false}, "darkTheme": {"default": "#097479", "baseColor": "#097479", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif", "edited": false}, "customTheme": {"name": "Untitled Theme 1", "default": "#4B7930", "baseColor": "#4B7930", "baseFont": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif"}, "themeState": {"base-color": {"default": "#0094CE", "value": "#0094CE", "edited": false}, "page-titlebar-backgroundColor": {"value": "#0094CE", "edited": false}, "page-backgroundColor": {"value": "#fafafa", "edited": false}, "page-sidebar-backgroundColor": {"value": "#ffffff", "edited": false}, "group-textColor": {"value": "#1bbfff", "edited": false}, "group-borderColor": {"value": "#ffffff", "edited": false}, "group-backgroundColor": {"value": "#ffffff", "edited": false}, "widget-textColor": {"value": "#111111", "edited": false}, "widget-backgroundColor": {"value": "#0094ce", "edited": false}, "widget-borderColor": {"value": "#ffffff", "edited": false}, "base-font": {"value": "-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif"}}, "angularTheme": {"primary": "indigo", "accents": "blue", "warn": "red", "background": "grey", "palette": "light"}}, "site": {"name": "Node-RED Dashboard", "hideToolbar": "false", "allowSwipe": "false", "lockMenu": "false", "allowTempTheme": "true", "dateFormat": "DD/MM/YYYY", "sizes": {"sx": 48, "sy": 48, "gx": 6, "gy": 6, "cx": 6, "cy": 6, "px": 0, "py": 0}}}, {"id": "6adc2b5.1c493d4", "type": "ui_group", "name": "Guage", "tab": "6a878cef.7a1294", "order": 1, "disp": true, "width": "6", "collapse": false}, {"id": "cfe29f74.cf003", "type": "ui_group", "name": "Chart", "tab": "6a878cef.7a1294", "order": 2, "disp": true, "width": "6", "collapse": false}, {"id": "54ebd45.e1cae2c", "type": "serial in", "z": "85039718.8a68d8", "name": "", "serial": "8e3887a.09b4278", "x": 110, "y": 100, "wires": [["28309c53.00fd54"]]}, {"id": "28309c53.00fd54", "type": "function", "z": "85039718.8a68d8", "name": "PH, EC, Temp", "func": "var output = msg.payload.split(\",\");\n\nvar ph = parseFloat(output[0]);\nvar ec = parseFloat(output[1]);\nvar temp = parseFloat(output[2]);\nvar msg1 = {payload:ph};\nvar msg2 = {payload:ec};\nvar msg3 = {payload:temp};\n\nreturn [msg1, msg2, msg3];\n", "outputs": 3, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 310, "y": 100, "wires": [["c7a7d21d.7865", "2550b30d.2bf13c", "345d3735.8d2478"], ["447a0f6b.f0a3e", "3d09bcb7.6a0894", "94ac9641.36dd28"], ["28006808.5c7468", "5386f259.323efc", "85a2be86.79b6b"]]}, {"id": "c7a7d21d.7865", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "6adc2b5.1c493d4", "order": 1, "width": 0, "height": 0, "gtype": "gage", "title": "PH Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": "10", "colors": ["#00b500", "#e6e600", "#d03e3e"], "seg1": "", "seg2": "", "x": 530, "y": 60, "wires": []}, {"id": "447a0f6b.f0a3e", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "6adc2b5.1c493d4", "order": 2, "width": 0, "height": 0, "gtype": "gage", "title": "EC Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": "10", "colors": ["#00b500", "#f1f104", "#ca3838"], "seg1": "", "seg2": "", "x": 530, "y": 100, "wires": []}, {"id": "28006808.5c7468", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "6adc2b5.1c493d4", "order": 3, "width": 0, "height": 0, "gtype": "gage", "title": "Temp Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": "70", "colors": ["#05bd05", "#e6e600", "#ca3838"], "seg1": "", "seg2": "", "x": 530, "y": 140, "wires": []}, {"id": "2550b30d.2bf13c", "type": "change", "z": "85039718.8a68d8", "name": "PH Sensor", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "ph", "tot": "str"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 530, "y": 180, "wires": [["aef0112.d6429f"]]}, {"id": "3d09bcb7.6a0894", "type": "change", "z": "85039718.8a68d8", "name": "EC Sensor", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "ec", "tot": "str"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 530, "y": 220, "wires": [["aef0112.d6429f"]]}, {"id": "5386f259.323efc", "type": "change", "z": "85039718.8a68d8", "name": "Temp Sensor", "rules": [{"t": "set", "p": "payload", "pt": "msg", "to": "temp", "tot": "str"}], "action": "", "property": "", "from": "", "to": "", "reg": false, "x": 530, "y": 260, "wires": [["aef0112.d6429f"]]}, {"id": "aef0112.d6429f", "type": "thingspeak42", "z": "85039718.8a68d8", "name": "Water Monitoring System - Thingspeak", "delay": "5", "topic1": "ph", "topic2": "ed", "topic3": "temp", "topic4": "", "topic5": "", "topic6": "", "topic7": "", "topic8": "", "endpoint": "https://thingspeak.com", "x": 830, "y": 220, "wires": []}, {"id": "345d3735.8d2478", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "cfe29f74.cf003", "order": 1, "width": 0, "height": 0, "label": "PH Sensor", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "x": 770, "y": 60, "wires": [[]]}, {"id": "85a2be86.79b6b", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "cfe29f74.cf003", "order": 3, "width": 0, "height": 0, "label": "Temp Sensor", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "x": 770, "y": 140, "wires": [[]]}, {"id": "94ac9641.36dd28", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "cfe29f74.cf003", "order": 2, "width": 0, "height": 0, "label": "EC Sensor", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "x": 770, "y": 100, "wires": [[]]}]