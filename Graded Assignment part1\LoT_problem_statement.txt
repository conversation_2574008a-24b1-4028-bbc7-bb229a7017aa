Smith Marine is a modern kelong located between Pulau Ubin and the Changi Point Ferry Terminal. 

As part of efforts to modernise operations, Smith Marine aims to adopt digital technologies to reduce operating costs and risks—especially those related to seafood livestock. 

Using data collected from IoT sensors deployed on-site, you are to submit an individual report based on the following components:

a.IoT System Architecture

Describe the overall architecture of the IoT system deployed at Smith Marine. Include the types of sensors used, how data is collected, transmitted, and stored, and the cloud-based platforms or services used for monitoring and analysis.

b.Photo of the IoT System

Attach a clear image showing the deployed hardware setup, including sensor placements, microcontrollers, and connectivity modules.

c.Arduino Source Code

Include the Arduino code used to interface with the sensors. Highlight and explain key parts of the code responsible for sensor readings, data formatting, and communication with external platforms.

d.Screenshot of the Node-RED Flow

Provide a screenshot of your Node-RED flow and explain its logic. Describe how the flow handles data input, processing, and output to the chosen platforms (e.g., dashboards or APIs).

e.Node-RED and ThingSpeak Dashboards

Show screenshots of your dashboards and explain the data visualisation components used. Describe how these dashboards can assist Smith Marine in monitoring environmental conditions and managing their seafood livestock more effectively.


C. Evaluation Criteria

Your submission will be assessed based on the following:

a.Clarity and accuracy of technical explanations
b.Demonstrated understanding of the IoT system and its components
c.Quality and readability of the Arduino code
d.Effectiveness of the Node-RED flow and dashboards
e.Individual analysis, critical reflection, and insights
Presentation and organisation of the report