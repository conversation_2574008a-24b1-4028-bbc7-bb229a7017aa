#!/usr/bin/env python
# coding: utf-8

# # Data Preparation for Laudromat Use Case
# 
# ***
# 
# ## Features description from the $CleandDataV20210515.csv$ file:
# 
# **index_col** time step for the washing cycle
# 
# **avC:** average current 
# 
# **avP:** average power
# 
# **avR:** average resistant
# 
# **maxC:** maximum current 
# 
# **maxP:** maximum power
# 
# **sdC:** standard deviation for current
# 
# **sdP:** standard deviation for power
# 
# **stdCR:** standard deviation for resistant
# 
# **stdCP:** standard deviation for power
# 
# **AvRR:** average relative resistance to previous resistance reading
# 
# **mode:** positive class: Daily Wash | negative class: Not Daily Wash
# 

# In[1]:


# Import necessary Python libraries
import numpy as np
import pandas as pd
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)
import pickle as pk
import seaborn as sns
import matplotlib.pyplot as plt
import os


# ### Data Preparation Process

# In[2]:


# Import necessary scikit-learn libraries
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# Load the dataset 
script_dir = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(script_dir, 'data', 'CleandDataV20210515.csv') 
currentdf  = pd.read_csv(data_path)

x = currentdf.iloc[:, 2:]
y = currentdf.iloc[:, 1]

# Display the full dataset 
#print (x)

# Splitting dataset into 75% for training and 25% for testing
X_train, X_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=2)

# Display the features and label from the training set 
print (X_train)
print (y_train)

# Standardize dataset 
mean = np.mean(X_train)
stddev = np.std(X_train)
X_train_sc = (X_train - mean) / stddev
X_test_sc = (X_test - mean) / stddev

# Plot distribution of average current (avC)
plt.figure(figsize=(8, 4))
sns.histplot(currentdf['avC'], kde=True)
plt.title('Distribution of Average Current (avC)')
plt.xlabel('avC')
plt.ylabel('Count')
plt.show()

# Plot correlation heatmap of features
plt.figure(figsize=(10, 8))
sns.heatmap(x.corr(), annot=True, fmt='.2f', cmap='coolwarm')
plt.title('Feature Correlation Matrix')
plt.show()


# We shall train some predictive models with this dataset in the next lesson...
# 
# ***

# 
# 
# ***
