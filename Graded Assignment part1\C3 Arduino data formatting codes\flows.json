[{"id": "23f187442a86c3e9", "type": "function", "z": "85039718.8a68d8", "name": "ThingSpeak", "func": "msg.method = \"GET\";\nmsg.url = \"https://api.thingspeak.com/update?api_key=53F76JCULJK1DYIS&field1=\" + msg.payload.temperature + \"&field2=\" + msg.payload.ph + \"&field3=\" + msg.payload.ec;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 530, "y": 540, "wires": [["88e135f6b7f947c8"]]}, {"id": "28309c53.00fd54", "type": "function", "z": "85039718.8a68d8", "name": "PH, EC, Temp", "func": "var output = msg.payload.split(\",\");\n\nvar ph = parseFloat(output[0]);\nvar ec = parseFloat(output[1]);\nvar temp = parseFloat(output[2]);\nvar msg1 = {payload:ph};\nvar msg2 = {payload:ec};\nvar msg3 = {payload:temp};\n\nreturn [msg1, msg2, msg3];\n", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 280, "y": 100, "wires": [["c7a7d21d.7865", "e43017569f34ec52", "a440037a67b3f12b", "af63a9dbb9d50b8c"], ["447a0f6b.f0a3e", "b9edb14882d68789", "172bb9da1ad71049", "30d5d186d61ac574"], ["28006808.5c7468", "ce3d2eefbe9d246b", "66ad245e672e86c8", "739ffb96eddb5b79"]]}, {"id": "8c3ab9b967c33848", "type": "function", "z": "85039718.8a68d8", "name": "Send data to ThingSpeak", "func": "var output =msg.payload.split(\",\");\nvar ph=parseFloat(output[0]);\nvar ec=parseFloat(output[1]);\nvar temp=parseFloat(output[2]);\nvar result={\n    ph:ph,\n    ec:ec,\n    temp:temp\n};\nmsg.payload=result;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 310, "y": 500, "wires": [["1c3bf6b18a622b6c", "23f187442a86c3e9"]]}]