Section 1 Research findings and references

Why Smart Fish Farms Are Betting Big on IoT
https://www.iotforall.com/why-smart-fish-farms-are-betting-big-on-iot

Artificial Intelligence and Its Aquaculture Applications
https://ecomagazine.com/news/fisheries-aquaculture/artificial-intelligence-and-its-aquaculture-applications/

Implementation of an IoT-based DC nanogrid in an offshore fish farm
https://ieeexplore.ieee.org/abstract/document/9293728

IOT farming system for pelagic fisheries based on blind satellite communication
https://ieeexplore.ieee.org/document/9980042

IoT-based smart monitoring and management system for fish farming
https://www.researchgate.net/publication/367207333_IoT-based_smart_monitoring_and_management_system_for_fish_farming

Implementation of an IoT-based DC nanogrid in an offshore fish farm
https://www.researchgate.net/publication/347865718_Implementation_of_an_IoT-based_DC_nanogrid_in_an_offshore_fish_farm

Development of smart aquaculture farm management system using IoT and AI-based surrogate models
https://www.sciencedirect.com/science/article/pii/S2666154322000904

Internet of Things in aquaculture: A review of the challenges and potential solutions based on current and future trends
https://www.sciencedirect.com/science/article/pii/S2772375523000175

Overview of Smart Aquaculture System: Focusing on Applications of Machine Learning and Computer Vision
https://www.mdpi.com/2079-9292/10/22/2882

Internet of Things is a revolutionary approach for future technology enhancement: a review
https://www.researchgate.net/publication/337846400_Internet_of_Things_is_a_revolutionary_approach_for_future_technology_enhancement_a_review

Digital twin-based intelligent fish farming with Artificial Intelligence Internet of Things (AIoT)
https://www.sciencedirect.com/science/article/pii/S2772375523001144


------------

Commercial Solutions

Revolutionizing Aquaculture with Smart Technology
Transform your fish farming operations with AI-powered monitoring, automated feeding systems, and real-time data analytics that reduce costs and maximize productivity.
https://rgwjjyzz.manus.space/

Digitally Transform Your Agriculture Businesses With Our One-Stop Farm Solution Platform
6 Easy Steps to Implement FarmERP’s Farm Business Management Software in 30 Days
https://digital.farmerp.com/?utm_source=google&utm_medium=CPC&utm_campaign&utm_term=iot%20farming%20solutions&utm_content=3&utm_medicum=ppc&gad_source=1&gad_campaignid=***********&gbraid=0AAAAAD37w6MuF9Cwmniszj_aSpBb7HeJo&gclid=Cj0KCQjwmK_CBhCEARIsAMKwcD77uZesjGCSfLpep6jIl2FOmFYYsshELQesOLFw9FAwgqZXj0-ClF0aAlriEALw_wcB

Arduino as an innovation platform
Arduino has drawn on this experience in frictionless design to enable enterprises to quickly and securely connect remote sensors to business logic within one simple IoT application development platform.
https://www.arduino.cc/pro/why-pro/

Raspberry Pi for industry
https://www.raspberrypi.com/for-industry/


----------------------

Lab 1

Introduction to IoT & Problem Statement
https://en.wikipedia.org/wiki/Kelong
https://www.straitstimes.com/singapore/environment/mass-fish-deaths-overnight-hit-changi-farmers-hard 
https://www.straitstimes.com/singapore/thousands-of-fish-dead-near-lim-chu-kang-jetty-warmer-temperatures-suspected-as-cause 

----------

Lab 2

Arduino Programming
http://arduino.cc/en/Main/Software
https://www.youtube.com/watch?v=3Gz3sWsU5HQ
https://docs.arduino.cc/programming/

----------

Lab 3

Node-Red Programming 
https://nodered.org/docs/getting-started/raspberrypi
https://www.raspberrypi.org/software/
https://www.realvnc.com/en/connect/download/viewer/
https://www.raspberrypi.org/help/what-%20is-a-raspberry-pi/

----------

Lab 4

ThingSpeak IoT Platform
https://thingspeak.mathworks.com/

----------

Lab 5

Mosquitto Broker
https://randomnerdtutorials.com/what-is-mqtt-and-how-it-works/

MQTT Programming using Node Red
https://www.youtube.com/watch?v=X8ustpkAJ-U
https://www.youtube.com/watch?v=Ho1vMuA43ks
https://www.youtube.com/watch?v=GUVERLcvXsI
https://www.youtube.com/watch?v=XDrwgMSQrEY

----------

Lab 6

Sensors Calibration
Node-Red workflow programming
ThingSpeak dashboard settings

----------

Lab 7 Assignment






