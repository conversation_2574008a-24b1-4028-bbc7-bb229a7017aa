#!/usr/bin/env python
# coding: utf-8

# # Classificatiom Model 3: Random Forest
# 
# ***
# Let's develop our random forest model for the laundromat use case. 
# 

# In[ ]:


from google.colab import drive

drive.mount(??, force_remount=True)


# In[ ]:


ROOT_DIR = ??


# In[11]:


# Import necessary Python libraries here ...
# ...
# ...
# ...

# Loading the dataset from the /data folder here 
data_path  = ?

# Read your csv file here ...
currentdf  = ???

# Allocate your training data and label 
x = ?
y = ?

# Splitting dataset into 75% for training and 25% for testing here ...
# ...
# ...

# Display the features and label from the training set 
print(?)
print(?)

# Insert code to standardize your dataset here ...
# ...
# ...


# ***
# ## Training vs Testing Error
# 
# As you have learnt 3 very useful classifiers, it is time to evaluate their accuracy on the testing set. In the training set, the 3 models appear to give us about the same arracy of around 80% - 90% range. However, we need a reference point to determine whether this 80% is good or bad.
# 
# We can compare the random forest model with the other two classifiers using the testing data:
# 

# In[14]:


# Retrain the KNN model with k=7
from sklearn.?? import ??Classifier
knn = KNeighborsClassifier(n_neighbors=??)
knn.fit(X_train, y_train) 

# Retrain the decision treen model with max_depth=6
from sklearn.?? import ??Classifier
class_tree = DecisionTreeClassifier(max_depth=??,
                                   min_samples_split=??) 
class_tree.fit(X_train, y_train)

# Retrain the random forest model with max_depth=6
from sklearn.?? import ??Classifier
rf = RandomForestClassifier(n_estimators=??,
                            max_depth=??,
                            random_state=??)
rf.fit(X_train, y_train)

# Prepare the data frame for evaluation metrics
accuracies = pd.DataFrame(columns=['Train', 'Test'], index=["KNN", 'DecisionTree', 'RandomForest'])
model_dict = {'KNN': knn, 'DecisionTree': class_tree, 'RandomForest': rf}

# Evaluate the accuraccies of the 3 predictive models
from sklearn.metrics import accuracy_score 
for name, model in model_dict.items():
    accuracies.loc[name, 'Train'] = accuracy_score(y_true=y_train, y_pred=model.predict(X_train))                                                                                                                  
    accuracies.loc[name, 'Test'] = accuracy_score(y_true=y_test, y_pred=model.predict(X_test))   

# Show results in percentage
100*accuracies  
  


# Let use the bar graph to virtally compare the 3 predictive models to evaluate the accuracy of both training and testing sets.

# In[15]:


fig, ax = plt.subplots()
accuracies.sort_values(by='Test', ascending=False).plot(kind='barh', ax=ax, zorder=3)
ax.grid(zorder=0); 


# From the accuracy results, discuss with your team on the following questions:
# 
# 1. Which predictive model would your team choose to adopt? And why? 
# 2. How will you improve the results of the accuracy further? 
# 3. Is Accuracy result alone is sufficient for you to justify that it is indeed a good predictive model? Why or why not? 

# Your response here ...

# ***
# You may save your 3 models for future use. 

# In[ ]:


import pickle as pk

...
...
...

print("Models Saved")


# ***
