{"cells": [{"cell_type": "markdown", "id": "2130b04d", "metadata": {"id": "2130b04d"}, "source": ["# Lab Demonstration - Random Forest\n", "****\n", "\n", "### Ensemble Learning\n", "\n", "One of the advantages of using decision trees is their simplicity. However, if the tree is small, it will lead to a weak predictor. Their performance may still not up to our expectation. In 1980s, a new method known as Ensemble methods or Ensemble Learning was invented. The core idea of Ensemble Learning is simple. Instead of using just one model to make predictions, we can use many individual models and combine their predictions. This simple idea has been one of the keys in the success of machine learning in producing very accurate models. In this lesson, we will introduce some of the concepts needed for having a high-level of how random forest work.\n", "\n", "### <PERSON>tra<PERSON> Sampling\n", "\n", "If you have a dataset $D$ of $n$ observations, then a bootstrap sample of the dataset will consist of $n$ samples randomly chosen with replacement from $D$. Suppose D = [1,2,3,4,5]; a bootstrap sample would be D* = [5,5,1,2,2]. We see repeated values because the sampling process was done with replacement.\n", "\n", "### Bagging\n", "\n", "Bagging comes from bootstrap aggregating, and it is the procedure of taking $K$ bootstrap samples of a dataset and then fitting $K$ models, one in each of the $K$ bootstrap sample datasets.At such, we would have $K$ models. We can aggregate (combine) their individual predictions by applying the majority vote rule in the case of classification. For example, if K=100 and for some observation, 75 out of 100 models classify the observation as \"default\". The final classification of the Bagging method would be \"default\". Bagging is the basis for the random forest method that applies a small variation to decorrelate individual predictors.\n", "\n", "\n", "## Random Forest\n", "\n", "They are called forest because the individual predictors are trees. The rationale is this: if your K=100 models will give you a very similar prediction, or the same prediction for all observations, then there is no point of using 100 models because they are giving you the same opinion. Decorrelating the individual predictors means to make sure a bit distinct from one another, so that they can offer a different perspective on the data. The basic mechanism that random forest implements to perform this decorrelation is to use only random sample of the feature in splitting tree.\n", "\n", "When a node is split during the construction of the tree, the chosen split is no longer the best split among all features. Instead, the split that is picked is the best split among a random subset of the features. Thus, if we have 15 predictors and the max_features parameter is 5, then only 5 randomly chosen features will be considered for the split. Once all the individual trees have been fit, the majority vote rule is applied to give the final prediction. Since the individual predictors are trees. We need to provide hyperparameters for both the individual trees and the ensemble itself.\n", "\n", "***"]}, {"cell_type": "markdown", "id": "d1c2d466", "metadata": {"id": "d1c2d466"}, "source": ["\n", "## Random Forest Classifier\n", "\n", "### Defining Dataset\n", "\n", "Let's create a dataset with two features (in blue ) and one label (in orange) that contain a record of weather, temperature and the decision to play tennis from the past 14 days. This dataset will be used to build a model to predict whether the weather and temperature today is suitable for you to play a tennis match outdoor.\n", "\n", "![table-3.png](attachment:table-3.png)\n"]}, {"cell_type": "code", "execution_count": 1, "id": "78cb44be", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "78cb44be", "executionInfo": {"status": "ok", "timestamp": 1749716908537, "user_tz": -480, "elapsed": 18946, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}, "outputId": "a120f449-7c78-4e27-9690-4268c921109a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "\n", "drive.mount('/content/drive', force_remount=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "5f0c3bcf", "metadata": {"id": "5f0c3bcf", "executionInfo": {"status": "ok", "timestamp": 1749716911937, "user_tz": -480, "elapsed": 6, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["ROOT_DIR = '/content/drive/MyDrive/C3790C'"]}, {"cell_type": "code", "execution_count": 3, "id": "41b6909a", "metadata": {"id": "41b6909a", "executionInfo": {"status": "ok", "timestamp": 1749716915376, "user_tz": -480, "elapsed": 4, "user": {"displayName": "<PERSON>", "userId": "08280565725279349310"}}}, "outputs": [], "source": ["# First Feature\n", "weather = ['<PERSON>','<PERSON>','Overcast','Rainy','Rainy','Rainy','Overcast','<PERSON>','<PERSON>',\n", "'Rainy','<PERSON>','Overcast','Overcast','Rainy']\n", "\n", "# Second Feature\n", "temp = ['Hot','Hot','Hot','Mild','Cool','Cool','Cool','Mild','Cool','Mild','Mild','Mild','Hot','Mild']\n", "\n", "# Label or target varible\n", "tennis = ['No','No','Yes','Yes','Yes','No','Yes','No','Yes','Yes','Yes','Yes','Yes','No']\n"]}, {"cell_type": "markdown", "id": "8489462d", "metadata": {"id": "8489462d"}, "source": ["### Encoding data columns\n", "\n", "Various machine learning algorithms require numerical input data, so you need to represent categorical columns in a numerical column.\n", "\n", "In order to encode this data, you could map each value to a number. e.g. Overcast:0, <PERSON><PERSON>:1, and <PERSON>:2.\n", "\n", "This process is known as label encoding, and $sklearn$ conveniently will do this for you using $Label Encoder$.\n"]}, {"cell_type": "code", "execution_count": null, "id": "44316899", "metadata": {"id": "44316899", "outputId": "a654eb33-b8ec-4e59-a8e0-fdac39569463"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2 2 0 1 1 1 0 2 2 1 2 0 0 1]\n"]}], "source": ["# Importing preprocessing library\n", "from sklearn import preprocessing\n", "\n", "# Creating a labelEncoder\n", "le = preprocessing.LabelEncoder()\n", "\n", "# Converting string labels into numbers.\n", "weather_encoded = le.fit_transform(weather)\n", "print (weather_encoded)\n"]}, {"cell_type": "markdown", "id": "a89c0027", "metadata": {"id": "a89c0027"}, "source": ["Here, you have imported preprocessing module and created Label Encoder object. Using this $LabelEncoder$ object, you can fit and transform \"weather\" column into the numeric column. Similarly, you can encode temperature and label into numeric columns.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2e2d44b1", "metadata": {"id": "2e2d44b1"}, "outputs": [], "source": ["# Converting string labels into numbers\n", "temp_encoded = le.fit_transform(temp)\n", "label = le.fit_transform(tennis)\n"]}, {"cell_type": "markdown", "id": "bec433cc", "metadata": {"id": "bec433cc"}, "source": ["### Combining Features\n", "\n", "You can combine multiple columns or features into a single set of data using \"zip\" function.\n"]}, {"cell_type": "code", "execution_count": null, "id": "69aa243e", "metadata": {"id": "69aa243e"}, "outputs": [], "source": ["# Combining weather and temp into a single list of tuples\n", "features = list(zip(weather_encoded, temp_encoded))\n"]}, {"cell_type": "markdown", "id": "71fb0166", "metadata": {"id": "71fb0166"}, "source": ["### Generating Model\n", "\n", "Let's develop our random forest model. You may refer to https://scikit-learn.org/stable/modules/generated/sklearn.ensemble.RandomForestClassifier.html for the different types of parameters available for random forest classifier. As the hyperparameters present in both individual trees and the ensemble make random forest a bit tricky to optimize, scikit-learn library usually give a good default parameters for you to work on.\n", "\n", "First, import the $RandomForestClassifier$ module and create Random Forest classifier object by passing argument 'n_estimators' that represent the number of trees in the forest. Let's set 'n_estimators' to be 99. For each tree, 'max_depth' defines the  maximum depth of the tree. If it is specified as 'None', then the nodes are expanded until all leaves are 'pure' or until all leaves contain less than 'min_samples_split' samples. For this simple demonstration, we shall set 'max_depth' to 2. The parameter 'random_state' controls both the randomness of the bootstrapping of the samples used when building trees and the sampling of the features to consider when looking for the best split at each node. We shall set the value of 'ransom_state'as 2.  \n", "\n", "Next, fit your model on the train set using $fit()$ before you perform your prediction on the test set with $predict()$.\n"]}, {"cell_type": "code", "execution_count": null, "id": "32f692a3", "metadata": {"id": "32f692a3", "outputId": "a463a17d-c1dc-4f54-c02a-5c4bf72ab3bc"}, "outputs": [{"data": {"text/plain": ["RandomForestClassifier(max_depth=2, n_estimators=99, random_state=2)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "\n", "model = RandomForestClassifier(n_estimators=99,\n", "                               max_depth=2,\n", "                               random_state=2)\n", "\n", "# Train the model using the training sets\n", "model.fit(features, label)\n"]}, {"cell_type": "markdown", "id": "426d03b8", "metadata": {"id": "426d03b8"}, "source": ["Assume today's weather is overcast and the temperature is mild, let's test the outcome of the Random Forest model to predict wether you should play tennis today."]}, {"cell_type": "code", "execution_count": null, "id": "4d82d680", "metadata": {"id": "4d82d680", "outputId": "43a5ead6-c387-431b-f830-41278e8eb8f8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1]\n"]}], "source": ["# Predict the Output\n", "predicted = model.predict([[0, 2]]) # 0:Overcast, 2:Mild\n", "print (predicted)\n"]}, {"cell_type": "markdown", "id": "755aa3eb", "metadata": {"id": "755aa3eb"}, "source": ["In the above demonstration, you have given input [0, 2], where 0 means Overcast weather and 2 means Mild temperature. Like previous machine learning models, our Random Forest model has also predicts [1], which means today's weather and temperature is suitable for you to play tennis.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 5}