[{"id": "23f187442a86c3e9", "type": "function", "z": "85039718.8a68d8", "name": "ThingSpeak", "func": "msg.method = \"GET\";\nmsg.url = \"https://api.thingspeak.com/update?api_key=53F76JCULJK1DYIS&field1=\" + msg.payload.temperature + \"&field2=\" + msg.payload.ph + \"&field3=\" + msg.payload.ec;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 530, "y": 540, "wires": [["88e135f6b7f947c8"]]}]