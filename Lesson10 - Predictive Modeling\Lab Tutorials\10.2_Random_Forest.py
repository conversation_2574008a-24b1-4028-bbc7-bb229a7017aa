#!/usr/bin/env python
# coding: utf-8

# # Classificatiom Model 3: Random Forest
# 
# ***
# Let's develop our random forest model for the laundromat use case. 
# 

# In[ ]:


from google.colab import drive

drive.mount(??, force_remount=True)


# In[ ]:


ROOT_DIR = ??


# In[11]:


# Import necessary Python libraries here ...
# ...
# ...
# ...

# Loading the dataset from the /data folder here 
data_path  = ?

# Read your csv file here ...
currentdf  = ???

# Allocate your training data and label 
x = ?
y = ?

# Splitting dataset into 75% for training and 25% for testing here ...
# ...
# ...

# Display the features and label from the training set 
print(?)
print(?)

# Insert code to standardize your dataset here ...
# ...
# ...


# Scikit-learn library usually give a good default parameters for you to work on. You may refer to https://scikit-learn.org/stable/modules/generated/sklearn.ensemble.RandomForestClassifier.html for the different types of parameters available for random forest classifier. 
# 

# In[12]:


from sklearn.?? import ??Classifier
from sklearn.metrics import accuracy_score 

# define the tree depths to evaluate
values = [i for i in range(0, 10)]
mdepth = [1, 3, 4, 5, 6, 7, 8, 9, 10, 11]
train_scores, test_scores = list(), list()

for i in values:
    # Create an instance of the predictor
    rf = RandomForestClassifier(n_estimators=??,
                            max_depth=??,
                            random_state=??)
     
    # Use the training data to train the predictor
    rf.fit(X_train_sc, y_train)
    
    # Predicting on the train dataset
    y_pred_train = rf.predict(X_train_sc)   
    train_acc = accuracy_score(y_train, y_pred_train)
    train_scores.append(train_acc)
    
    # Predicting on the test dataset
    y_pred_test = rf.predict(X_test_sc)    
    test_acc = accuracy_score(y_test, y_pred_test)
    test_scores.append(test_acc)
    


# In[13]:


# plot of train and test scores vs tree depth
plt.plot(values, train_scores, '-o', label='Train')
plt.plot(values, test_scores, '-o', label='Test')
plt.legend()
plt.show()


# We get approximately 0.85 or 85% for the test set, which is not too far from what we got from the other models.

# ## Model Evaluation
# 
# ### Confusion Matrix
# 
# Let's compute the confusion matrix for this Decision Tree model for the laudromat use case.
# 

# In[16]:


from sklearn.?? import ??

# Determine the accuracy of the model
score = rf.score(X_test_sc, y_test)
class_label=['CottonWash', 'CottonRinse', 'CottonSpin', 'BeddingWash', 'BeddingRinse', 'BeddingSpin',
                 'DailyWash', 'DailyRinse','DailySpin']

rf.fit(??, ??)
rf.predict(??)
cm = confusion_matrix(y_test, rf.predict(??), labels=class_label)
#
axes = sns.heatmap(cm, square=True, annot=True, fmt ='d', cbar=True, cmap=plt.cm.GnBu)
axes.set_ylabel('Actual')
axes.set_ylabel('Predication')
tick_marks = np.arange(len(class_label)) + 0.5
axes.set_xticks(tick_marks)
axes.set_xticklabels(class_label, rotation = 90)
axes.set_yticks(tick_marks)
axes.set_yticklabels(class_label, rotation = 0)
axes.set_title('Confusion Matrix')
plt.show()

print("Model Score {}" .format( score) )


# Finally, let's calculate the Accuracy, Precision, Recall and F1 Score metrics for the Random Forest model.
# 

# In[17]:


from sklearn.metrics import ??, ??, ??

rf =??Classifier(n_estimators=??,
                            max_depth=9,
                            random_state=??)
rf.fit(??, ??)

accuracy = accuracy_score(??, y_pred_test)
precision = precision_score(??, y_pred_test, average='macro')
recall = recall_score(??, y_pred_test, average='macro')
f1 = f1_score(??, y_pred_test, average='macro')

print ("Accuracy: {:0.1f}%, Precision: {:0.1f}%, Recall: {:0.1f}%, F1 Score: {:0.1f}%".format(100*accuracy, 100* precision, 100*recall, 100*f1))


# Finally, are the 4 metrics calculated for random forest better or worst than KNN and Decision Tree models? Which model will you propose for your stakeholder to adopt? Justify your reasons for choosing it?
# 

# Your response here ... 
# 

# You may save your random forest model for future use. 
# 

# In[18]:


import pickle as pk

model_filename = ?? + "/model/rf.mdl"
with open(model_filename, "wb") as file:
    pk.dump(rf, file)
print("Model Saved")


# ***
