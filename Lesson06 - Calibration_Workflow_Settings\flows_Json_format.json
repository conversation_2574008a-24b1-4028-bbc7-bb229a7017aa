[{"id": "85039718.8a68d8", "type": "tab", "label": "Flow 1", "disabled": false, "info": ""}, {"id": "54ebd45.e1cae2c", "type": "serial in", "z": "85039718.8a68d8", "name": "", "serial": "8e3887a.09b4278", "x": 90, "y": 60, "wires": [["28309c53.00fd54", "8c3ab9b967c33848"]]}, {"id": "28309c53.00fd54", "type": "function", "z": "85039718.8a68d8", "name": "PH, EC, Temp", "func": "var output = msg.payload.split(\",\");\n\nvar ph = parseFloat(output[0]);\nvar ec = parseFloat(output[1]);\nvar temp = parseFloat(output[2]);\nvar msg1 = {payload:ph};\nvar msg2 = {payload:ec};\nvar msg3 = {payload:temp};\n\nreturn [msg1, msg2, msg3];\n", "outputs": 3, "timeout": "", "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 280, "y": 100, "wires": [["c7a7d21d.7865", "e43017569f34ec52", "a440037a67b3f12b", "af63a9dbb9d50b8c"], ["447a0f6b.f0a3e", "b9edb14882d68789", "172bb9da1ad71049", "30d5d186d61ac574"], ["28006808.5c7468", "ce3d2eefbe9d246b", "66ad245e672e86c8", "739ffb96eddb5b79"]]}, {"id": "c7a7d21d.7865", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "6adc2b5.1c493d4", "order": 1, "width": 0, "height": 0, "gtype": "gage", "title": " Gauge PH Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": "10", "colors": ["#00b500", "#e6e600", "#d03e3e"], "seg1": "", "seg2": "", "diff": false, "className": "", "x": 550, "y": 60, "wires": []}, {"id": "447a0f6b.f0a3e", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "6adc2b5.1c493d4", "order": 2, "width": 0, "height": 0, "gtype": "gage", "title": "Gauge EC Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": "10", "colors": ["#00b500", "#f1f104", "#ca3838"], "seg1": "", "seg2": "", "diff": false, "className": "", "x": 550, "y": 100, "wires": []}, {"id": "28006808.5c7468", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "6adc2b5.1c493d4", "order": 3, "width": 0, "height": 0, "gtype": "gage", "title": "Gauge Temp Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": "70", "colors": ["#05bd05", "#e6e600", "#ca3838"], "seg1": "", "seg2": "", "diff": false, "className": "", "x": 560, "y": 140, "wires": []}, {"id": "e43017569f34ec52", "type": "debug", "z": "85039718.8a68d8", "name": "pH", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 410, "y": 260, "wires": []}, {"id": "b9edb14882d68789", "type": "debug", "z": "85039718.8a68d8", "name": "eC", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 390, "y": 320, "wires": []}, {"id": "ce3d2eefbe9d246b", "type": "debug", "z": "85039718.8a68d8", "name": "temp", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 370, "y": 380, "wires": []}, {"id": "a440037a67b3f12b", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "8773d41353121931", "order": 1, "width": 0, "height": 0, "label": "PH", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "className": "", "x": 830, "y": 60, "wires": [[]]}, {"id": "172bb9da1ad71049", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "8773d41353121931", "order": 2, "width": 0, "height": 0, "label": "EC", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "className": "", "x": 830, "y": 100, "wires": [[]]}, {"id": "66ad245e672e86c8", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "8773d41353121931", "order": 3, "width": 0, "height": 0, "label": "Temp", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "className": "", "x": 850, "y": 140, "wires": [[]]}, {"id": "8c3ab9b967c33848", "type": "function", "z": "85039718.8a68d8", "name": "Send data to ThingSpeak", "func": "var output =msg.payload.split(\",\");\nvar ph=parseFloat(output[0]);\nvar ec=parseFloat(output[1]);\nvar temp=parseFloat(output[2]);\nvar result={\n    ph:ph,\n    ec:ec,\n    temp:temp\n};\nmsg.payload=result;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 310, "y": 500, "wires": [["1c3bf6b18a622b6c", "23f187442a86c3e9"]]}, {"id": "1c3bf6b18a622b6c", "type": "debug", "z": "85039718.8a68d8", "name": "msg payload", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 530, "y": 480, "wires": []}, {"id": "23f187442a86c3e9", "type": "function", "z": "85039718.8a68d8", "name": "ThingSpeak", "func": "msg.method = \"GET\";\nmsg.url = \"https://api.thingspeak.com/update?api_key=53F76JCULJK1DYIS&field1=\" + msg.payload.temperature + \"&field2=\" + msg.payload.ph + \"&field3=\" + msg.payload.ec;\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 530, "y": 540, "wires": [["88e135f6b7f947c8"]]}, {"id": "88e135f6b7f947c8", "type": "http request", "z": "85039718.8a68d8", "name": "", "method": "use", "ret": "txt", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [], "x": 730, "y": 540, "wires": [[]]}, {"id": "a70b434ed7c65a32", "type": "mqtt in", "z": "85039718.8a68d8", "name": "", "topic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qos": "2", "datatype": "auto-detect", "broker": "8ae1a7c620750c0d", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 720, "y": 200, "wires": [["ac125e23fafbadc9", "3053f9b7efee130c", "0b6c91a7c2c90429"]]}, {"id": "af63a9dbb9d50b8c", "type": "mqtt out", "z": "85039718.8a68d8", "name": "", "topic": "john<PERSON>", "qos": "", "retain": "", "respTopic": "", "contentType": "", "userProps": "", "correl": "", "expiry": "", "broker": "8ae1a7c620750c0d", "x": 630, "y": 320, "wires": []}, {"id": "30d5d186d61ac574", "type": "mqtt out", "z": "85039718.8a68d8", "name": "", "topic": "johne<PERSON>", "qos": "", "retain": "", "respTopic": "", "contentType": "", "userProps": "", "correl": "", "expiry": "", "broker": "8ae1a7c620750c0d", "x": 650, "y": 380, "wires": []}, {"id": "739ffb96eddb5b79", "type": "mqtt out", "z": "85039718.8a68d8", "name": "", "topic": "joh<PERSON><PERSON>", "qos": "", "retain": "", "respTopic": "", "contentType": "", "userProps": "", "correl": "", "expiry": "", "broker": "8ae1a7c620750c0d", "x": 680, "y": 440, "wires": []}, {"id": "ac125e23fafbadc9", "type": "debug", "z": "85039718.8a68d8", "name": "debug 1", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 1020, "y": 420, "wires": []}, {"id": "921a5b1e29d3d96b", "type": "mqtt in", "z": "85039718.8a68d8", "name": "", "topic": "ch<PERSON><PERSON><PERSON><PERSON>", "qos": "2", "datatype": "auto-detect", "broker": "8ae1a7c620750c0d", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 740, "y": 260, "wires": [["3288bdc57d718bd0", "0a2e584e870fee03", "689d299f16623fc8"]]}, {"id": "3288bdc57d718bd0", "type": "debug", "z": "85039718.8a68d8", "name": "debug 2", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1020, "y": 480, "wires": []}, {"id": "eef1ccc022904898", "type": "mqtt in", "z": "85039718.8a68d8", "name": "", "topic": "chenjietemp", "qos": "2", "datatype": "auto-detect", "broker": "8ae1a7c620750c0d", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 770, "y": 320, "wires": [["20085d11356ad43e", "5f6ffc3e9e4faf42", "276c9eef411d7577"]]}, {"id": "20085d11356ad43e", "type": "debug", "z": "85039718.8a68d8", "name": "debug 3", "active": false, "tosidebar": true, "console": false, "tostatus": false, "complete": "false", "statusVal": "", "statusType": "auto", "x": 1020, "y": 540, "wires": []}, {"id": "3053f9b7efee130c", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "763f357c55cac93e", "order": 1, "width": 0, "height": 0, "gtype": "gage", "title": " Gauge PH Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": 10, "colors": ["#00b500", "#e6e600", "#ca3838"], "seg1": "", "seg2": "", "diff": false, "className": "", "x": 970, "y": 180, "wires": []}, {"id": "0a2e584e870fee03", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "763f357c55cac93e", "order": 2, "width": 0, "height": 0, "gtype": "gage", "title": " Gauge EC Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": 10, "colors": ["#00b500", "#e6e600", "#ca3838"], "seg1": "", "seg2": "", "diff": false, "className": "", "x": 970, "y": 220, "wires": []}, {"id": "5f6ffc3e9e4faf42", "type": "ui_gauge", "z": "85039718.8a68d8", "name": "", "group": "763f357c55cac93e", "order": 3, "width": 0, "height": 0, "gtype": "gage", "title": " Gauge Temp Sensor", "label": "units", "format": "{{value}}", "min": 0, "max": 10, "colors": ["#00b500", "#e6e600", "#ca3838"], "seg1": "", "seg2": "", "diff": false, "className": "", "x": 980, "y": 260, "wires": []}, {"id": "0b6c91a7c2c90429", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "2d030c1462a79fd3", "order": 1, "width": 0, "height": 0, "label": "PH", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "className": "", "x": 930, "y": 300, "wires": [[]]}, {"id": "689d299f16623fc8", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "2d030c1462a79fd3", "order": 2, "width": 0, "height": 0, "label": "EC", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "className": "", "x": 970, "y": 340, "wires": [[]]}, {"id": "276c9eef411d7577", "type": "ui_chart", "z": "85039718.8a68d8", "name": "", "group": "2d030c1462a79fd3", "order": 3, "width": 0, "height": 0, "label": "Temp", "chartType": "line", "legend": "false", "xformat": "HH:mm:ss", "interpolate": "linear", "nodata": "", "dot": false, "ymin": "", "ymax": "", "removeOlder": 1, "removeOlderPoints": "", "removeOlderUnit": "3600", "cutout": 0, "useOneColor": false, "useUTC": false, "colors": ["#1f77b4", "#aec7e8", "#ff7f0e", "#2ca02c", "#98df8a", "#d62728", "#ff9896", "#9467bd", "#c5b0d5"], "outputs": 1, "useDifferentColor": false, "className": "", "x": 990, "y": 380, "wires": [[]]}, {"id": "8e3887a.09b4278", "type": "serial-port", "name": "", "serialport": "/dev/ttyACM0", "serialbaud": "115200", "databits": "8", "parity": "none", "stopbits": "1", "waitfor": "", "dtr": "none", "rts": "none", "cts": "none", "dsr": "none", "newline": "\\n", "bin": "false", "out": "char", "addchar": "", "responsetimeout": "10000"}, {"id": "6adc2b5.1c493d4", "type": "ui_group", "name": "Gauge", "tab": "6a878cef.7a1294", "order": 1, "disp": true, "width": "6", "collapse": false, "className": ""}, {"id": "8773d41353121931", "type": "ui_group", "name": "Chart", "tab": "6a878cef.7a1294", "order": 2, "disp": true, "width": 6, "collapse": false, "className": ""}, {"id": "8ae1a7c620750c0d", "type": "mqtt-broker", "name": "", "broker": "https://4.193.104.36", "port": 1883, "clientid": "", "autoConnect": true, "usetls": false, "protocolVersion": 4, "keepalive": 60, "cleansession": true, "autoUnsubscribe": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "763f357c55cac93e", "type": "ui_group", "name": "chenjieguage", "tab": "6a878cef.7a1294", "order": 3, "disp": true, "width": 6, "collapse": false, "className": ""}, {"id": "2d030c1462a79fd3", "type": "ui_group", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tab": "6a878cef.7a1294", "order": 4, "disp": true, "width": 6, "collapse": false, "className": ""}, {"id": "6a878cef.7a1294", "type": "ui_tab", "name": "Water Monitoring System", "icon": "Gauge", "disabled": false, "hidden": false}]